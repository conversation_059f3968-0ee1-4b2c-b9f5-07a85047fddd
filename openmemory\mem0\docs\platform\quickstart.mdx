---
title: Guide
description: 'Get started with Mem0 Platform in minutes'
icon: "book"
iconType: "solid"
---

<Snippet file="paper-release.mdx" />

<Note type="info">
  🎉 Looking for TypeScript support? Mem0 has you covered! Check out an example [here](/platform/quickstart/#4-11-working-with-mem0-in-typescript).
</Note>

## 1. Installation

<CodeGroup>
```bash pip
pip install mem0ai
```

```bash npm
npm install mem0ai
```

</CodeGroup>

## 2. API Key Setup

1. Sign in to [Mem0 Platform](https://mem0.dev/pd-api)
2. Copy your API Key from the dashboard

![Get API Key from Mem0 Platform](/images/platform/api-key.png)

## 3. Instantiate Client

<CodeGroup>
```python Python
import os
from mem0 import MemoryClient

os.environ["MEM0_API_KEY"] = "your-api-key"

client = MemoryClient()
```

```javascript JavaScript
import MemoryClient from 'mem0ai';
const client = new MemoryClient({ apiKey: 'your-api-key' });
```

</CodeGroup>

### 3.1 Instantiate Async Client (Python only)

For asynchronous operations in Python, you can use the AsyncMemoryClient:

```python Python
import os
from mem0 import AsyncMemoryClient

os.environ["MEM0_API_KEY"] = "your-api-key"

client = AsyncMemoryClient()


async def main():
    messages = [
        {"role": "user", "content": "I'm travelling to SF"}
    ]
    response = await client.add(messages, user_id="john")
    print(response)

await main()
```

## 4. Memory Operations

Mem0 provides a simple and customizable interface for performing CRUD operations on memory.

### 4.1 Create Memories

#### Long-term memory for a user

These memory instances persist across multiple sessions. Ideal for maintaining memory over long time spans.

<CodeGroup>

```python Python
messages = [
    {"role": "user", "content": "Hi, I'm Alex. I'm a vegetarian and I'm allergic to nuts."},
    {"role": "assistant", "content": "Hello Alex! I've noted that you're a vegetarian and have a nut allergy. I'll keep this in mind for any food-related recommendations or discussions."}
]

client.add(messages, user_id="alex", metadata={"food": "vegan"})
```

```javascript JavaScript
const messages = [
    {"role": "user", "content": "Hi, I'm Alex. I'm a vegetarian and I'm allergic to nuts."},
    {"role": "assistant", "content": "Hello Alex! I've noted that you're a vegetarian and have a nut allergy. I'll keep this in mind for any food-related recommendations or discussions."}
];
client.add(messages, { user_id: "alex", metadata: { food: "vegan" } })
    .then(response => console.log(response))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "messages": [
             {"role": "user", "content": "Hi, I'm Alex. I'm a vegetarian and I'm allergic to nuts."},
             {"role": "assistant", "content": "Hello Alex! I've noted that you're a vegetarian and have a nut allergy. I'll keep this in mind for any food-related recommendations or discussions."}
         ],
         "user_id": "alex",
         "metadata": {
             "food": "vegan"
         }
     }'
```

```json Output
{
    "results": [
        {
            "memory": "Name is Alex",
            "event": "ADD"
        },
        {
            "memory": "Is a vegetarian",
            "event": "ADD"
        },
        {
            "memory": "Is allergic to nuts",
            "event": "ADD"
        }
    ]
}
```

</CodeGroup>

<Note>
    When passing `user_id`, memories are primarily created based on user messages, but may be influenced by assistant messages for contextual understanding. For example, in a conversation about food preferences, both the user's stated preferences and their responses to the assistant's questions would form user memories. Similarly, when using `agent_id`, assistant messages are prioritized, but user messages might influence the agent's memories based on context. This approach ensures comprehensive memory creation while maintaining appropriate attribution to either users or agents.
    
    **Example:**
    ```
    User: My favorite cuisine is Italian
    Assistant: Nice! What about Indian cuisine?
    User: Don't like it much since I cannot eat spicy food
    
    Resulting user memories:
    memory1 - Likes Italian food
    memory2 - Doesn't like Indian food since cannot eat spicy 
    
    (memory2 comes from user's response about Indian cuisine)
    ```
</Note>

<Note>Metadata allows you to store structured information (location, timestamp, user state) with memories. Add it during creation to enable precise filtering and retrieval during searches.</Note>


#### Short-term memory for a user session

These memory instances persist only for the duration of a user session. Ideal for non-repetitive interactions and managing context windows efficiently.

<CodeGroup>

```python Python
messages = [
    {"role": "user", "content": "I'm planning a trip to Japan next month."},
    {"role": "assistant", "content": "That's exciting, Alex! A trip to Japan next month sounds wonderful. Would you like some recommendations for vegetarian-friendly restaurants in Japan?"},
    {"role": "user", "content": "Yes, please! Especially in Tokyo."},
    {"role": "assistant", "content": "Great! I'll remember that you're interested in vegetarian restaurants in Tokyo for your upcoming trip. I'll prepare a list for you in our next interaction."}
]

client.add(messages, user_id="alex", run_id="trip-planning-2024")
```

```javascript JavaScript
const messages = [
    {"role": "user", "content": "I'm planning a trip to Japan next month."},
    {"role": "assistant", "content": "That's exciting, Alex! A trip to Japan next month sounds wonderful. Would you like some recommendations for vegetarian-friendly restaurants in Japan?"},
    {"role": "user", "content": "Yes, please! Especially in Tokyo."},
    {"role": "assistant", "content": "Great! I'll remember that you're interested in vegetarian restaurants in Tokyo for your upcoming trip. I'll prepare a list for you in our next interaction."}
];
client.add(messages, { user_id: "alex", run_id: "trip-planning-2024" })
    .then(response => console.log(response))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "messages": [
             {"role": "user", "content": "I'm planning a trip to Japan next month."},
             {"role": "assistant", "content": "That's exciting, Alex! A trip to Japan next month sounds wonderful. Would you like some recommendations for vegetarian-friendly restaurants in Japan?"},
             {"role": "user", "content": "Yes, please! Especially in Tokyo."},
             {"role": "assistant", "content": "Great! I'll remember that you're interested in vegetarian restaurants in Tokyo for your upcoming trip. I'll prepare a list for you in our next interaction."}
         ],
         "user_id": "alex",
         "run_id": "trip-planning-2024"
     }'
```

```json Output
{
  "results": [
    {
      "memory": "Planning a trip to Japan next month",
      "event": "ADD"
    },
    {
      "memory": "Interested in vegetarian restaurants in Tokyo",
      "event": "ADD"
    }
  ]
}
```

</CodeGroup>

#### Long-term memory for agents
Add a memory layer for the assistants and agents so that their responses remain consistent across sessions. 

<CodeGroup>

```python Python
messages = [
    {"role": "system", "content": "You are an AI tutor with a personality. Give yourself a name for the user."},
    {"role": "assistant", "content": "Understood. I'm an AI tutor with a personality. My name is Alice."}
]

client.add(messages, agent_id="ai-tutor")
```

```javascript JavaScript
const messages = [
    {"role": "system", "content": "You are an AI tutor with a personality. Give yourself a name for the user."},
    {"role": "assistant", "content": "Understood. I'm an AI tutor with a personality. My name is Alice."}
];
client.add(messages, { agent_id: "ai-tutor" })
    .then(response => console.log(response))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "messages": [
             {"role": "system", "content": "You are an AI tutor with a personality. Give yourself a name for the user."},
             {"role": "assistant", "content": "Understood. I'm an AI tutor with a personality. My name is Alice."}
         ],
         "agent_id": "ai-tutor"
     }'
```

```json Output
{
    "results": [
        {
            "memory": "Name is Alex",
            "event": "ADD"
        },
        {
            "memory": "Is a vegetarian",
            "event": "ADD"
        },
        {
            "memory": "Is allergic to nuts",
            "event": "ADD"
        }
    ]
}
```
</CodeGroup>

<Note>
    The `agent_id` retains memories exclusively based on messages generated by the assistant or those explicitly provided as input to the assistant. Messages outside these criteria are not stored as memory.
</Note>

#### Long-term memory for both users and agents
When you provide both `user_id` and `agent_id`, Mem0 will store memories for both identifiers separately:
- Memories from messages with `"role": "user"` are automatically tagged with the provided `user_id`
- Memories from messages with `"role": "assistant"` are automatically tagged with the provided `agent_id`
- During retrieval, you can provide either `user_id` or `agent_id` to access the respective memories
- You can continuously enrich existing memory collections by adding new memories to the same `user_id` or `agent_id` in subsequent API calls, either together or separately, allowing for progressive memory building over time
- This dual-tagging approach enables personalized experiences for both users and AI agents in your application

<CodeGroup>

```python Python
messages = [
    {"role": "user", "content": "I'm travelling to San Francisco"},
    {"role": "assistant", "content": "That's great! I'm going to Dubai next month."},
]

client.add(messages=messages, user_id="user1", agent_id="agent1")
```

```javascript JavaScript
const messages = [
    {"role": "user", "content": "I'm travelling to San Francisco"},
    {"role": "assistant", "content": "That's great! I'm going to Dubai next month."},
]

client.add(messages, { user_id: "user1", agent_id: "agent1" })
    .then(response => console.log(response))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "messages": [
             {"role": "user", "content": "I'm travelling to San Francisco"},
             {"role": "assistant", "content": "That's great! I'm going to Dubai next month."},
         ],
         "user_id": "user1",
         "agent_id": "agent1"
     }'
```

```json Output
{
    "results": [
        {
            // memory from user1
            "id": "c57abfa2-f0ac-48af-896a-21728dbcecee0",
            "data": {"memory": "Travelling to San Francisco"},
            "event": "ADD"
        },
        {
            // memory from agent1
            "id": "0e8c003f-7db7-426a-9fdc-a46f9331a0c2",
            "data": {"memory": "Going to Dubai next month"},
            "event": "ADD"
        }
    ]
}
```

</CodeGroup>

#### Monitor Memories

You can monitor memory operations on the platform dashboard:

![Mem0 Platform Activity](/images/platform/activity.png)

### 4.2 Search Memories

#### General Memory Search

Pass user messages, interactions, and queries into our search method to retrieve relevant memories.

<Note> The `search` method supports two output formats: `v1.0` (default) and `v1.1`. To use the latest format, which provides more detailed information about each memory operation, set the `output_format` parameter to `v1.1`: </Note>

<CodeGroup>

```python Python
query = "What should I cook for dinner today?"

client.search(query, user_id="alex")
```

```javascript JavaScript
const query = "What should I cook for dinner today?";
client.search(query, { user_id: "alex", output_format: "v1.1" })
    .then(results => console.log(results))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/search/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "query": "What should I cook for dinner today?",
         "user_id": "alex",
         "output_format": "v1.1"
     }'
```

```json Output
{
    "results": [
        {
            "id": "7f165f7e-b411-4afe-b7e5-35789b72c4a5",
            "memory": "Vegetarian. Allergic to nuts.",
            "user_id": "alex",
            "metadata": {"food": "vegan"},
            "categories": ["food_preferences"],
            "immutable": false,
            "expiration_date": null,
            "created_at": "2024-07-20T01:30:36.275141-07:00",
            "updated_at": "2024-07-20T01:30:36.275172-07:00"
        }
    ]
}
```
</CodeGroup>

Use category and metadata filters:

<CodeGroup>

```python Python
query = "What do you know about me?"

client.search(query, categories=["food_preferences"], metadata={"food": "vegan"})
```

```javascript JavaScript
const query = "What do you know about me?";
client.search(query, categories=["food_preferences"], metadata={"food": "vegan"})
    .then(results => console.log(results))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/search/?version=v2" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "query": "What do you know about me?",
         "categories": ["food_preferences"],
         "metadata": {"food": "vegan"}
     }'
```

```json Output
{
    "results": [
        {
            "id": "7f165f7e-b411-4afe-b7e5-35789b72c4a5",
            "memory": "Name: Alex. Vegetarian. Allergic to nuts.",
            "user_id": "alex",
            "metadata": {"food": "vegan"},
            "categories": ["food_preferences"],
            "immutable": false,
            "expiration_date": null,
            "created_at": "2024-07-20T01:30:36.275141-07:00",
            "updated_at": "2024-07-20T01:30:36.275172-07:00"
        }
    ]
}
```
</CodeGroup>


#### Search using custom filters

Our advanced search allows you to set custom search filters. You can filter by user_id, agent_id, app_id, run_id, created_at, updated_at, categories, and text. The filters support logical operators (AND, OR) and comparison operators (in, gte, lte, gt, lt, ne, contains, icontains). For more details, see [V2 Search Memories](/api-reference/memory/v2-search-memories).

Here you need to define `version` as `v2` in the search method.

Example 1: Search using user_id and agent_id filters

<CodeGroup>

```python Python
query = "What do you know about me?"
filters = {
   "OR":[
      {
         "user_id":"alex"
      },
      {
         "agent_id":{
            "in":[
               "travel-assistant",
               "customer-support"
            ]
         }
      }
   ]
}
client.search(query, version="v2", filters=filters)
```

```javascript JavaScript
const query = "What do you know about me?";
const filters = {
   "OR":[
      {
         "user_id":"alex"
      },
      {
         "agent_id":{
            "in":[
               "travel-assistant",
               "customer-support"
            ]
         }
      }
   ]
};
client.search(query, { version: "v2", filters })
    .then(results => console.log(results))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/search/?version=v2" \
     -H "Authorization : Token your - api - key" \
     -H "Content-Type : application / json" \
     -d '{
         "query": "What do you know about me?",
         "filters": {
             "OR": [
                 {
                     "user_id": "alex"
                 },
                 {
                     "agent_id": {
                         "in": ["travel-assistant", "customer-support"]
                     }
                 }
             ]
         }
     }'
```

```json Output
{
    "results": [
        {
            "id": "7f165f7e-b411-4afe-b7e5-35789b72c4a5",
            "memory": "Name: Alex. Vegetarian. Allergic to nuts.",
            "user_id": "alex",
            "metadata": null,
            "categories": ["food_preferences"],
            "immutable": false,
            "expiration_date": null,
            "created_at": "2024-07-20T01:30:36.275141-07:00",
            "updated_at": "2024-07-20T01:30:36.275172-07:00"
        }
    ]
}
```
</CodeGroup>

Example 2: Search using date filters
<CodeGroup>
```python Python
query = "What do you know about me?"
filters = {
    "AND": [
        {"created_at": {"gte": "2024-07-20", "lte": "2024-07-10"}},
        {"user_id": "alex"}
    ]
}
client.search(query, version="v2", filters=filters)
```

```javascript JavaScript
const query = "What do you know about me?";
const filters = {
  "AND": [
    {"created_at": {"gte": "2024-07-20", "lte": "2024-07-10"}},
    {"user_id": "alex"}
  ]
};

client.search(query, { version: "v2", filters })
  .then(results => console.log(results))
  .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/search/?version=v2" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "query": "What do you know about me?",
         "filters": {
             "AND": [
                 {
                     "created_at": {
                         "gte": "2024-07-20",
                         "lte": "2024-07-10"
                     }
                 },
                 {
                     "user_id": "alex"
                 }
             ]
         }
     }'
```

```json Output
{
    "results": [
        {
            "id": "7f165f7e-b411-4afe-b7e5-35789b72c4a5",
            "memory": "Name: Alex. Vegetarian. Allergic to nuts.",
            "user_id": "alex",
            "metadata": null,
            "categories": ["food_preferences"],
            "immutable": false,
            "expiration_date": null,
            "created_at": "2024-07-20T01:30:36.275141-07:00",
            "updated_at": "2024-07-20T01:30:36.275172-07:00"
        }
    ]
}
```
</CodeGroup>

Example 3: Search using metadata and categories Filters
<CodeGroup>
```python Python
query = "What do you know about me?"
filters = {
    "AND": [
        {"metadata": {"food": "vegan"}},
        {
         "categories":{
            "contains": "food_preferences"
         }
    }
    ]
}
client.search(query, version="v2", filters=filters)
```

```javascript JavaScript
const query = "What do you know about me?";
const filters = {
    "AND": [
        {"metadata": {"food": "vegan"}},
        {
            "categories": {
                "contains": "food_preferences"
            }
        }
    ]
};

client.search(query, { version: "v2", filters })
    .then(results => console.log(results))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/search/?version=v2" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "query": "What do you know about me?",
         "filters": {
             "AND": [
                 {
                     "metadata": {
                         "food": "vegan"
                     }
                 },
                 {
                     "categories": {
                         "contains": "food_preferences"
                     }
                 }
             ]
         }
     }'
```

```json Output
{
    "results": [
        {
            "id": "654fee-b411-4afe-b7e5-35789b72c4a5",
            "memory": "Name: Alex. Vegetarian. Allergic to nuts.",
            "user_id": "alex",
            "metadata": {"food": "vegan"},
            "categories": ["food_preferences"],
            "immutable": false,
            "expiration_date": null,
            "created_at": "2024-07-20T01:30:36.275141-07:00",
            "updated_at": "2024-07-20T01:30:36.275172-07:00"
        }
    ]
}
```

</CodeGroup>

Example 4: Search using NOT filters
<CodeGroup>
```python Python
query = "What do you know about me?"
filters = {
    "NOT": [
        {
            "categories": {
                "contains": "food_preferences"
            }
        }
    ]
}
client.search(query, version="v2", filters=filters)
```

```javascript JavaScript
const query = "What do you know about me?";
const filters = {
    "NOT": [
        {
            "categories": {
                "contains": "food_preferences"
            }
        }
    ]
};

client.search(query, { version: "v2", filters })
    .then(results => console.log(results))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/search/?version=v2" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "query": "What do you know about me?",
         "filters": {
            "NOT": [
                {
                    "categories": {
                        "contains": "food_preferences"
                    }
                }
            ]
        }
     }'
```

```json Output
{
    "results": [
        {
            "id": "123abc-d456-7890-efgh-ijklmnopqrst",
            "memory": "Lives in San Francisco",
            "user_id": "alex",
            "metadata": null,
            "categories": ["location"],
            "immutable": false,
            "expiration_date": null,
            "created_at": "2024-07-20T01:30:36.275141-07:00",
            "updated_at": "2024-07-20T01:30:36.275172-07:00"
        }
    ]
}
```

</CodeGroup>


### 4.3 Get All Users

Get all users, agents, and runs which have memories associated with them.

<CodeGroup>

```python Python
client.users()
```

```javascript JavaScript
client.users()
    .then(users => console.log(users))
    .catch(error => console.error(error));
```

```bash cURL
curl -X GET "https://api.mem0.ai/v1/entities/" \
     -H "Authorization: Token your-api-key"
```


```json Output
[
    {
        "id": "1",
        "name": "user123",
        "created_2024-07-17T16:47:23.899900-07:00",
        "updated_at": "2024-07-17T16:47:23.899918-07:00",
        "total_memories": 5,
        "owner": "alex",
        "metadata": {"foo": "bar"},
        "type": "user"
    },
    {
        "id": "2",
        "name": "travel-agent",
        "created_at": "2024-07-01T17:59:08.187250-07:00",
        "updated_at": "2024-07-01T17:59:08.187266-07:00",
        "total_memories": 10,
        "owner": "alex",
        "metadata": {"agent_id": "123"},
        "type": "agent"
    }
]
```

</CodeGroup>


### 4.4 Get All Memories

Fetch all memories for a user, agent, or run using the getAll() method.

<Note> The `get_all` method supports two output formats: `v1.0` (default) and `v1.1`. To use the latest format, which provides more detailed information about each memory operation, set the `output_format` parameter to `v1.1`: </Note>
<Note> We're soon deprecating the default output format for get_all() method, which returned a list. Once the changes are live, paginated response will be the only supported format, with 100 memories per page by default. You can customize this using the `page` and `page_size` parameters. </Note>

The following examples showcase the paginated output format. 

#### Get all memories of a user

<CodeGroup>

```python Python
memories = client.get_all(user_id="alex", page=1, page_size=50)
```

```javascript JavaScript
client.getAll({ user_id: "alex", page: 1, page_size: 50 })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));
```

```bash cURL
curl -X GET "https://api.mem0.ai/v1/memories/?user_id=alex&page=1&page_size=50" \
     -H "Authorization: Token your-api-key"
```

```json Output (v1.1)
{
    "count": 204,
    "next": "https://api.mem0.ai/v1/memories/?user_id=alex&output_format=v1.1&page=2&page_size=50",
    "previous": null,
    "results": 
    [
            {
                "id":"f38b689d-6b24-45b7-bced-17fbb4d8bac7",
                "memory":"是素食主义者，对坚果过敏。",
                "agent_id":"travel-assistant",
                "hash":"62bc074f56d1f909f1b4c2b639f56f6a",
                "metadata":None,
                "immutable": false,
                "expiration_date": null,
                "created_at":"2024-07-25T23:57:00.108347-07:00",
                "updated_at":"2024-07-25T23:57:00.108367-07:00",
                "categories":None
            },
            {
                "id":"0a14d8f0-e364-4f5c-b305-10da1f0d0878",
                "memory":"Will maintain personalized travel preferences for each user. Provide customized recommendations based on dietary restrictions, interests, and past interactions.",
                "agent_id":"travel-assistant",
                "hash":"35a305373d639b0bffc6c2a3e2eb4244",
                "metadata":"None",
                "immutable": false,
                "expiration_date": null,
                "created_at":"2024-07-26T00:31:03.543759-07:00",
                "updated_at":"2024-07-26T00:31:03.543778-07:00",
                "categories":None
            }
            ... (remaining 48 memories)
    ]
}
```

</CodeGroup>


#### Get all memories of an AI Agent

<CodeGroup>
```python Python
agent_memories = client.get_all(agent_id="ai-tutor", page=1, page_size=50)
```

```javascript JavaScript
client.getAll({ agent_id: "ai-tutor", page: 1, page_size: 50 })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));
```

```bash cURL
curl -X GET "https://api.mem0.ai/v1/memories/?agent_id=ai-tutor&page=1&page_size=50" \
     -H "Authorization: Token your-api-key"
```

```json Output (v1.1)
{
    "count": 78,
    "next": "https://api.mem0.ai/v1/memories/?agent_id=ai-tutor&output_format=v1.1&page=2&page_size=50",
    "previous": null,
    "results": 
    [
            {
                "id": "f38b689d-6b24-45b7-bced-17fbb4d8bac7",
                "memory": "是素食主义者，对坚果过敏。",
                "agent_id": "ai-tutor",
                "hash": "62bc074f56d1f909f1b4c2b639f56f6a",
                "metadata":None,
                "immutable": false,
                "expiration_date": null,
                "created_at": "2024-07-25T23:57:00.108347-07:00",
                "updated_at": "2024-07-25T23:57:00.108367-07:00"
            },
            {
                "id": "0a14d8f0-e364-4f5c-b305-10da1f0d0878",
                "memory": "My name is Alice.",
                "agent_id": "ai-tutor",
                "hash": "35a305373d639b0bffc6c2a3e2eb4244",
                "metadata":None,
                "immutable": false,
                "expiration_date": null,
                "created_at": "2024-07-26T00:31:03.543759-07:00",
                "updated_at": "2024-07-26T00:31:03.543778-07:00"
            }
            ... (remaining 48 memories)
    ]
}
```
</CodeGroup>

#### Get the short-term memories for a session

<CodeGroup>

```python Python
short_term_memories = client.get_all(user_id="alex", run_id="trip-planning-2024", page=1, page_size=50)
```

```javascript JavaScript
client.getAll({ user_id: "alex", run_id: "trip-planning-2024", page: 1, page_size: 50 })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));
```

```bash cURL
curl -X GET "https://api.mem0.ai/v1/memories/?user_id=alex&run_id=trip-planning-2024&page=1&page_size=50" \
     -H "Authorization: Token your-api-key"
```

```json Output
{
    "count": 18,
    "next": null,
    "previous": null,
    "results": 
       [
            {
                "id": "06d8df63-7bd2-4fad-9acb-60871bcecee0",
                "memory": "Planning a trip to Japan next month. Interested in vegetarian restaurants in Tokyo.",
                "user_id": "alex",
                "hash": "d2088c936e259f2f5d2d75543d31401c",
                "metadata":None,
                "immutable": false,
                "expiration_date": null,
                "created_at": "2024-07-26T00:25:16.566471-07:00",
                "updated_at": "2024-07-26T00:25:16.566492-07:00",
                "categories": ["food_preferences"]
            },
            {
                "id": "b4229775-d860-4ccb-983f-0f628ca112f5",
                "memory": "Planning a trip to Japan next month. Interested in vegetarian restaurants in Tokyo.",
                "user_id": "alex",
                "hash": "d2088c936e259f2f5d2d75543d31401c",
                "metadata":None,
                "immutable": false,
                "expiration_date": null,
                "created_at": "2024-07-26T00:33:20.350542-07:00",
                "updated_at": "2024-07-26T00:33:20.350560-07:00",
                "categories": ["food_preferences"]
            },
            {
                "id": "df1aca24-76cf-4b92-9f58-d03857efcb64",
                "memory": "Planning a trip to Japan next month. Interested in vegetarian restaurants in Tokyo.",
                "user_id": "alex",
                "hash": "d2088c936e259f2f5d2d75543d31401c",
                "metadata":None,
                "immutable": false,
                "expiration_date": null,
                "created_at": "2024-07-26T00:51:09.642275-07:00",
                "updated_at": "2024-07-26T00:51:09.642295-07:00",
                "categories": None
            }
            ... (remaining 15 memories)
        ]
}
```
</CodeGroup>


#### Get specific memory

<CodeGroup>

```python Python
memory = client.get(memory_id="582bbe6d-506b-48c6-a4c6-5df3b1e63428")
```

```javascript JavaScript
client.get("582bbe6d-506b-48c6-a4c6-5df3b1e63428")
    .then(memory => console.log(memory))
    .catch(error => console.error(error));
```

```bash cURL
curl -X GET "https://api.mem0.ai/v1/memories/582bbe6d-506b-48c6-a4c6-5df3b1e63428" \
     -H "Authorization: Token your-api-key"
```

```json Output
{
   "id":"06d8df63-7bd2-4fad-9acb-60871bcecee0",
   "memory":"Planning a trip to Japan next month. Interested in vegetarian restaurants in Tokyo.",
   "user_id":"alex",
   "hash":"d2088c936e259f2f5d2d75543d31401c",
   "metadata":"None",
   "immutable": false,
   "expiration_date": null,
   "created_at":"2024-07-26T00:25:16.566471-07:00",
   "updated_at":"2024-07-26T00:25:16.566492-07:00",
   "categories": ["travel"]
}
```
</CodeGroup>

#### Get all memories by categories

You can filter memories by their categories when using get_all:

<CodeGroup>

```python Python
# Get memories with specific categories
memories = client.get_all(user_id="alex", categories=["likes"])

# Get memories with multiple categories
memories = client.get_all(user_id="alex", categories=["likes", "food_preferences"])

# Custom pagination with categories
memories = client.get_all(user_id="alex", categories=["likes"], page=1, page_size=50)

# Get memories with specific keywords
memories = client.get_all(user_id="alex", keywords="to play", page=1, page_size=50)
```

```javascript JavaScript
// Get memories with specific categories
client.getAll({ user_id: "alex", categories: ["likes"] })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));

// Get memories with multiple categories
client.getAll({ user_id: "alex", categories: ["likes", "food_preferences"] })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));

// Custom pagination with categories
client.getAll({ user_id: "alex", categories: ["likes"], page: 1, page_size: 50 })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));

// Get memories with specific keywords
client.getAll({ user_id: "alex", keywords: "to play", page: 1, page_size: 50 })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));
```

```bash cURL
# Get memories with specific categories
curl -X GET "https://api.mem0.ai/v1/memories/?user_id=alex&categories=likes" \
     -H "Authorization: Token your-api-key"

# Get memories with multiple categories
curl -X GET "https://api.mem0.ai/v1/memories/?user_id=alex&categories=likes,food_preferences" \
     -H "Authorization: Token your-api-key"

# Custom pagination with categories
curl -X GET "https://api.mem0.ai/v1/memories/?user_id=alex&categories=likes&page=1&page_size=50" \
     -H "Authorization: Token your-api-key"

# Get memories with specific keywords
curl -X GET "https://api.mem0.ai/v1/memories/?user_id=alex&keywords=to play&page=1&page_size=50" \
     -H "Authorization: Token your-api-key"
```

```json Output(Paginated)
{
    "count": 2,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": "06d8df63-7bd2-4fad-9acb-60871bcecee0",
            "memory": "Likes pizza and pasta",
            "user_id": "alex",
            "hash": "d2088c936e259f2f5d2d75543d31401c",
            "metadata": null,
            "immutable": false,
            "expiration_date": null,
            "created_at": "2024-07-26T00:25:16.566471-07:00",
            "updated_at": "2024-07-26T00:25:16.566492-07:00",
            "categories": ["likes", "food_preferences"]
        },
        {
            "id": "b4229775-d860-4ccb-983f-0f628ca112f5",
            "memory": "Likes to travel to beach destinations",
            "user_id": "alex",
            "hash": "d2088c936e259f2f5d2d75543d31401c",
            "metadata": null,
            "immutable": false,
            "expiration_date": null,
            "created_at": "2024-07-26T00:33:20.350542-07:00",
            "updated_at": "2024-07-26T00:33:20.350560-07:00",
            "categories": ["likes"]
        }
    ]
}
```

</CodeGroup>

#### Get all memories using custom filters

Our advanced retrieval allows you to set custom filters when fetching memories. You can filter by user_id, agent_id, app_id, run_id, created_at, updated_at, categories, and keywords. The filters support logical operators (AND, OR) and comparison operators (in, gte, lte, gt, lt, ne, contains, icontains). For more details, see [v2 Get Memories](/api-reference/memory/v2-get-memories).

Here you need to define `version` as `v2` in the get_all method.

Example 1. Get all memories using user_id and date filters

<CodeGroup>

```python Python
filters = {
   "AND":[
      {
         "user_id":"alex"
      },
      {
         "created_at":{
            "gte":"2024-07-01",
            "lte":"2024-07-31"
         }
      },
      {
         "categories":{
            "contains": "food_preferences"
         }
      }
   ]
}

# Default (No Pagination)
client.get_all(version="v2", filters=filters)

# Pagination (You can also use the page and page_size parameters)
client.get_all(version="v2", filters=filters, page=1, page_size=50)
```

```javascript JavaScript
const filters = {
   "AND":[
      {
         "user_id":"alex"
      },
      {
         "created_at":{
            "gte":"2024-07-01",
            "lte":"2024-07-31"
         }
      },
      {
         "categories":{
            "contains": "food_preferences"
         }
      }
   ]
};

// Default (No Pagination)
client.getAll({ version: "v2", filters })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));

// Pagination (You can also use the page and page_size parameters)
client.getAll({ version: "v2", filters, page: 1, page_size: 50 })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));
```

```bash cURL
# Default (No Pagination)
curl -X GET "https://api.mem0.ai/v1/memories/?version=v2" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "filters": {
             "AND": [
                {"user_id":"alex"},
                {"created_at":{
                    "gte":"2024-07-01",
                    "lte":"2024-07-31"
                }},
                {"categories":{
                    "contains": "food_preferences"
                }}
             ]
         }
     }'

# Pagination (You can also use the page and page_size parameters)
curl -X GET "https://api.mem0.ai/v1/memories/?version=v2&page=1&page_size=50" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "filters": {
             "AND": [
                {"user_id":"alex"},
                {"created_at":{
                    "gte":"2024-07-01",
                    "lte":"2024-07-31"
                }},
                {"categories":{
                    "contains": "food_preferences"
                }}
             ]
         }
     }'
```

```json Output (Default)
[
   {
      "id":"f38b689d-6b24-45b7-bced-17fbb4d8bac7",
      "memory":"Name: Alex. Vegetarian. Allergic to nuts.",
      "user_id":"alex",
      "hash":"62bc074f56d1f909f1b4c2b639f56f6a",
      "metadata":null,
      "immutable": false,
      "expiration_date": null,
      "created_at":"2024-07-25T23:57:00.108347-07:00",
      "updated_at":"2024-07-25T23:57:00.108367-07:00",
      "categories": ["food_preferences"]
   }
]
```

```json Output (Paginated)
{
    "count": 1,
    "next": null,
    "previous": null,
    "results": [
        {
            "id":"f38b689d-6b24-45b7-bced-17fbb4d8bac7",
            "memory":"Name: Alex. Vegetarian. Allergic to nuts.",
            "user_id":"alex",
            "hash":"62bc074f56d1f909f1b4c2b639f56f6a",
            "metadata":null,
            "immutable": false,
            "expiration_date": null,
            "created_at":"2024-07-25T23:57:00.108347-07:00",
            "updated_at":"2024-07-25T23:57:00.108367-07:00",
            "categories": ["food_preferences"]
        }
    ]
}
```

</CodeGroup>

Example 2: Search using metadata and categories Filters
<CodeGroup>
```python Python
filters = {
    "AND": [
        {"metadata": {"food": "vegan"}},
        {
         "categories":{
            "contains": "food_preferences"
         }
    }
    ]
}
# Default (No Pagination)
client.get_all(version="v2", filters=filters)

# Pagination (You can also use the page and page_size parameters)
client.get_all(version="v2", filters=filters, page=1, page_size=50)
```

```javascript JavaScript
const filters = {
    "AND": [
        {"metadata": {"food": "vegan"}},
        {
            "categories": {
                "contains": "food_preferences"
            }
        }
    ]
};

// Default (No Pagination)
client.getAll({ version: "v2", filters })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));

// Pagination (You can also use the page and page_size parameters)
client.getAll({ version: "v2", filters, page: 1, page_size: 50 })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));
```

```bash cURL
# Default (No Pagination)
curl -X GET "https://api.mem0.ai/v1/memories/?version=v2" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "filters": {
             "AND": [
                {"metadata": {"food": "vegan"}},
                {
                    "categories": {
                        "contains": "food_preferences"
                    }
                }}
             ]
         }
     }'

# Pagination (You can also use the page and page_size parameters)
curl -X GET "https://api.mem0.ai/v1/memories/?version=v2&page=1&page_size=50" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "filters": {
                    "AND": [
                {"metadata": {"food": "vegan"}},
                {
                    "categories": {
                        "contains": "food_preferences"
                    }
                }}
            ]
         }
     }'
```

```json Output
[
    {
        "id": "654fee-b411-4afe-b7e5-35789b72c4a5",
        "memory": "Name: Alex. Vegetarian. Allergic to nuts.",
        "user_id": "alex",
        "metadata": {"food": "vegan"},
        "categories": ["food_preferences"],
        "immutable": false,
        "expiration_date": null,
        "created_at": "2024-07-20T01:30:36.275141-07:00",
        "updated_at": "2024-07-20T01:30:36.275172-07:00"
    }
]
```

</CodeGroup>

Example 3: Get all memories using NOT filters
<CodeGroup>
```python Python
filters = {
    "NOT": [
        {
            "categories": {
                "contains": "food_preferences"
            }
        }
    ]
}

# Default (No Pagination)
client.get_all(version="v2", filters=filters)

# Pagination (You can also use the page and page_size parameters)
client.get_all(version="v2", filters=filters, page=1, page_size=50)
```

```javascript JavaScript
const filters = {
    "NOT": [
        {
            "categories": {
                "contains": "food_preferences"
            }
        }
    ]
};

// Default (No Pagination)
client.getAll({ version: "v2", filters })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));

// Pagination (You can also use the page and page_size parameters)
client.getAll({ version: "v2", filters, page: 1, page_size: 50 })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));
```

```bash cURL
# Default (No Pagination)
curl -X GET "https://api.mem0.ai/v1/memories/?version=v2" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "filters": {
            "NOT": [
                {
                    "categories": {
                        "contains": "food_preferences"
                    }
                }
            ]
        }
     }'

# Pagination (You can also use the page and page_size parameters)
curl -X GET "https://api.mem0.ai/v1/memories/?version=v2&page=1&page_size=50" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "filters": {
            "NOT": [
                {
                    "categories": {
                        "contains": "food_preferences"
                    }
                }
            ]
        }
     }'
```

```json Output
[
    {
        "id": "789xyz-e012-3456-fghi-jklmnopqrstu",
        "memory": "Works as a software engineer",
        "user_id": "alex",
        "metadata": {"job": "tech"},
        "categories": ["work"],
        "immutable": false,
        "expiration_date": null,
        "created_at": "2024-07-20T01:30:36.275141-07:00",
        "updated_at": "2024-07-20T01:30:36.275172-07:00"
    }
]
```

```json Output (Paginated)
{
    "count": 1,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": "789xyz-e012-3456-fghi-jklmnopqrstu",
            "memory": "Works as a software engineer",
            "user_id": "alex",
            "metadata": {"job": "tech"},
            "categories": ["work"],
            "immutable": false,
            "expiration_date": null,
            "created_at": "2024-07-20T01:30:36.275141-07:00",
            "updated_at": "2024-07-20T01:30:36.275172-07:00"
        }
    ]
}
```

</CodeGroup>


### 4.5 Memory History

Get history of how a memory has changed over time.

<CodeGroup>

```python Python
# Add some message to create history
messages = [{"role": "user", "content": "I recently tried chicken and I loved it. I'm thinking of trying more non-vegetarian dishes.."}]
client.add(messages, user_id="alex")

# Add second message to update history
messages.append({'role': 'user', 'content': 'I turned vegetarian now.'})
client.add(messages, user_id="alex")

# Get history of how memory changed over time
memory_id = "<memory-id-here>"
history = client.history(memory_id)
```

```javascript JavaScript
// Add some message to create history
let messages = [{ role: "user", content: "I recently tried chicken and I loved it. I'm thinking of trying more non-vegetarian dishes.." }];
client.add(messages, { user_id: "alex" })
    .then(result => {
        // Add second message to update history
        messages.push({ role: 'user', content: 'I turned vegetarian now.' });
        return client.add(messages, { user_id: "alex" });
    })
    .then(result => {
        // Get history of how memory changed over time
        const memoryId = result.id; // Assuming the API returns the memory ID
        return client.history(memoryId);
    })
    .then(history => console.log(history))
    .catch(error => console.error(error));
```

```bash cURL
# First, add the initial memory
curl -X POST "https://api.mem0.ai/v1/memories/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "messages": [{"role": "user", "content": "I recently tried chicken and I loved it. I'm thinking of trying more non-vegetarian dishes.."}],
         "user_id": "alex"
     }'

# Then, update the memory
curl -X POST "https://api.mem0.ai/v1/memories/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "messages": [
             {"role": "user", "content": "I recently tried chicken and I loved it. I'm thinking of trying more non-vegetarian dishes.."},
             {"role": "user", "content": "I turned vegetarian now."}
         ],
         "user_id": "alex"
     }'

# Finally, get the history (replace <memory-id-here> with the actual memory ID)
curl -X GET "https://api.mem0.ai/v1/memories/<memory-id-here>/history/" \
     -H "Authorization: Token your-api-key"
```

```json Output
[
   {
      "id":"d6306e85-eaa6-400c-8c2f-ab994a8c4d09",
      "memory_id":"b163df0e-ebc8-4098-95df-3f70a733e198",
      "input":[
         {
            "role":"user",
            "content":"I recently tried chicken and I loved it. I'm thinking of trying more non-vegetarian dishes.."
         },
         {
            "role":"user",
            "content":"I turned vegetarian now."
         }
      ],
      "old_memory":"None",
      "new_memory":"Turned vegetarian.",
      "user_id":"alex",
      "event":"ADD",
      "metadata":"None",
      "created_at":"2024-07-26T01:02:41.737310-07:00",
      "updated_at":"2024-07-26T01:02:41.726073-07:00"
   }
]
```
</CodeGroup>

### 4.6 Update Memory

Update a memory with new data.

<CodeGroup>

```python Python
message = "I recently tried chicken and I loved it. I'm thinking of trying more non-vegetarian dishes.."
client.update(memory_id, message)
```

```javascript JavaScript
const message = "I recently tried chicken and I loved it. I'm thinking of trying more non-vegetarian dishes..";
client.update("memory-id-here", message)
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

```bash cURL
curl -X PUT "https://api.mem0.ai/v1/memories/memory-id-here" \
        -H "Authorization: Token your-api-key" \
        -H "Content-Type: application/json" \
        -d '{
            "message": "I recently tried chicken and I loved it. I'm thinking of trying more non-vegetarian dishes.."
        }'
```

```json Output
{
   "id":"c190ab1a-a2f1-4f6f-914a-495e9a16b76e",
   "memory":"I recently tried chicken and I loved it. I'm thinking of trying more non-vegetarian dishes..",
   "agent_id":"travel-assistant",
   "hash":"af1161983e03667063d1abb60e6d5c06",
   "metadata":"None",
   "created_at":"2024-07-30T22:46:40.455758-07:00",
   "updated_at":"2024-07-30T22:48:35.257828-07:00"
}
```

</CodeGroup>

### 4.7 Delete Memory

Delete specific memory.

<CodeGroup>

```python Python
client.delete(memory_id)
```

```javascript JavaScript
client.delete("memory-id-here")
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

```bash cURL
curl -X DELETE "https://api.mem0.ai/v1/memories/memory-id-here" \
     -H "Authorization: Token your-api-key"
```

```json Output
{'message': 'Memory deleted successfully'}
```
</CodeGroup>

Delete all memories of a user.

<CodeGroup>

```python Python
client.delete_all(user_id="alex")
```

```javascript JavaScript
client.deleteAll({ user_id: "alex" })
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

```bash cURL
curl -X DELETE "https://api.mem0.ai/v1/memories/?user_id=alex" \
     -H "Authorization: Token your-api-key"
```

```json Output
{'message': 'Memories deleted successfully!'}
```

</CodeGroup>

Delete all users.

<CodeGroup>

```python Python
client.delete_users()
```

```javascript JavaScript
client.delete_users()
    .then(users => console.log(users))
    .catch(error => console.error(error));
```

```json Output
{'message': 'All users, agents, and runs deleted.'}
```

</CodeGroup>
Delete specific user or agent or app or run.

<CodeGroup>
```python Python
# Delete specific user
client.delete_users(user_id="alex")

# Delete specific agent
# client.delete_users(agent_id="travel-assistant")
```

```javascript JavaScript
client.delete_users({ user_id: "alex" })
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

```bash cURL
curl -X DELETE "https://api.mem0.ai/v2/entities/user/alex" \
     -H "Authorization: Token your-api-key"
```

```json Output
{'message': 'Entity deleted successfully.'}
```

</CodeGroup>

### 4.8 Reset Client

<CodeGroup>

```python Python
client.reset()
```

```json Output
{'message': 'Client reset successful. All users and memories deleted.'}
```

</CodeGroup>


Fun fact: You can also delete the memory using the `add()` method by passing a natural language command:

<CodeGroup>

```python Python
messages = [
    {"role": "user", "content": "Delete all of my food preferences"}
]
client.add(messages, user_id="alex")
```

```javascript JavaScript
const messages = [
    {"role": "user", "content": "Delete all of my food preferences"}
]
client.add(messages, { user_id: "alex" })
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "messages": [{"role": "user", "content": "Delete all of my food preferences"}],
         "user_id": "alex"
     }'
```

```json Output
[{'id': '3f4eccba-3b09-497a-81ab-cca1ababb36b',
  'memory': 'Is allergic to nuts',
  'event': 'DELETE'},
 {'id': 'f5dcfbf4-5f0b-422a-8ad4-cadb9e941e25',
  'memory': 'Is a vegetarian',
  'event': 'DELETE'},
 {'id': 'dd32f70c-fa69-4fc7-997b-fb4a66d1a0fa',
  'memory': 'Name is Alex',
  'event': 'DELETE'}]{'message': 'ok'}
  {'id': '3f4eccba-3b09-497a-81ab-cca1ababb36b',
  'memory': 'Likes Chicken',
  'event': 'DELETE'}
```

</CodeGroup>

### 4.9 Batch Update Memories
Update multiple memories in a single API call. You can update up to 1000 memories at once.
<CodeGroup>
```python Python
update_memories = [
    {
        "memory_id": "285ed74b-6e05-4043-b16b-3abd5b533496",
        "text": "Watches football"
    },
    {
        "memory_id": "2c9bd859-d1b7-4d33-a6b8-94e0147c4f07",
        "text": "Loves to travel"
    }
]

response = client.batch_update(update_memories)
print(response)
```
```javascript JavaScript
const updateMemories = [{"memory_id": "285ed74b-6e05-4043-b16b-3abd5b533496",
        text: "Watches football"
    },
    {"memory_id": "2c9bd859-d1b7-4d33-a6b8-94e0147c4f07",
        text: "Loves to travel"
    }
];

client.batchUpdate(updateMemories)
    .then(response => console.log('Batch update response:', response))
    .catch(error => console.error(error));
```
```bash cURL
curl -X PUT "https://api.mem0.ai/v1/memories/batch/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "memories": [
             {
                 "memory_id": "285ed74b-6e05-4043-b16b-3abd5b533496",
                 "text": "Watches football"
             },
             {
                 "memory_id": "2c9bd859-d1b7-4d33-a6b8-94e0147c4f07",
                 "text": "Loves to travel"
             }
         ]
     }'
```
```json Output
{
    "message": "Successfully updated 2 memories"
}
```
</CodeGroup>
### 4.10 Batch Delete Memories
Delete multiple memories in a single API call. You can delete up to 1000 memories at once.
<CodeGroup>
```python Python
delete_memories = [
    {"memory_id": "285ed74b-6e05-4043-b16b-3abd5b533496"},
    {"memory_id": "2c9bd859-d1b7-4d33-a6b8-94e0147c4f07"}
]

response = client.batch_delete(delete_memories)
print(response)
```
```javascript JavaScript
const deleteMemories = [{"memory_id": "285ed74b-6e05-4043-b16b-3abd5b533496"},
    {"memory_id": "2c9bd859-d1b7-4d33-a6b8-94e0147c4f07"}
];

client.batchDelete(deleteMemories)
    .then(response => console.log('Batch delete response:', response))
    .catch(error => console.error(error));
```
```bash cURL
curl -X DELETE "https://api.mem0.ai/v1/memories/batch/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "memory_ids": [
             {"memory_id": "285ed74b-6e05-4043-b16b-3abd5b533496"},
             {"memory_id": "2c9bd859-d1b7-4d33-a6b8-94e0147c4f07"}
         ]
     }'
```
```json Output
{
    "message": "Successfully deleted 2 memories"
}
```
</CodeGroup>

### 4.11 Working with Mem0 in TypeScript
Manage memories using TypeScript with Mem0. Mem0 has completet TypeScript support Below is an example demonstrating how to add and search memories.

<CodeGroup>
```typescript TypeScript
import MemoryClient, { Message, SearchOptions, MemoryOptions }  from 'mem0ai';

const apiKey = 'your-api-key-here';
const client = new MemoryClient(apiKey);

// Messages
const messages: Message[] = [
    { role: "user", content: "Hi, I'm Alex. I'm a vegetarian and I'm allergic to nuts." },
    { role: "assistant", content: "Hello Alex! I've noted that you're a vegetarian and have a nut allergy. I'll keep this in mind for any food-related recommendations or discussions." }
];

// ADD
const memoryOptions: MemoryOptions = {
    user_id: "alex",
    agent_id: "travel-assistant"
}

client.add(messages, memoryOptions)
  .then(result => console.log(result))
  .catch(error => console.error(error));

// SEARCH
const query: string = "What do you know about me?";
const searchOptions: SearchOptions = {
    user_id: "alex",
    filters: {
        OR: [
          { agent_id: "travel-assistant" },
          { user_id: "alex" }
        ]
      },
      threshold: 0.1,
      api_version: 'v2'
}
  
client.search(query, searchOptions)
.then(results => console.log(results))
.catch(error => console.error(error));
```
</CodeGroup>

If you have any questions, please feel free to reach out to us using one of the following methods:

<Snippet file="get-help.mdx" />