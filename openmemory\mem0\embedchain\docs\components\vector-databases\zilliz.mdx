---
title: Zilliz
---

Install related dependencies using the following command:

```bash
pip install --upgrade 'embedchain[milvus]'
```

Set the Zilliz environment variables `ZILLIZ_CLOUD_URI` and `Z<PERSON>LIZ_CLOUD_TOKEN` which you can find it on their [cloud platform](https://cloud.zilliz.com/).

<CodeGroup>

```python main.py
import os
from embedchain import App

os.environ['ZILLIZ_CLOUD_URI'] = 'https://xxx.zillizcloud.com'
os.environ['ZILLIZ_CLOUD_TOKEN'] = 'xxx'

# load zilliz configuration from yaml file
app = App.from_config(config_path="config.yaml")
```

```yaml config.yaml
vectordb:
  provider: zilliz
  config:
    collection_name: 'zilliz_app'
    uri: https://xxxx.api.gcp-region.zillizcloud.com
    token: xxx
    vector_dim: 1536
    metric_type: L2
```

</CodeGroup>

<Snippet file="missing-vector-db-tip.mdx" />
