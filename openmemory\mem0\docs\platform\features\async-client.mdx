---
title: Async Client
description: 'Asynchronous client for Mem0'
icon: "bolt"
iconType: "solid"
---

<Snippet file="paper-release.mdx" />
The `AsyncMemoryClient` is an asynchronous client for interacting with the Mem0 API. It provides similar functionality to the synchronous `MemoryClient` but allows for non-blocking operations, which can be beneficial in applications that require high concurrency.

## Initialization

To use the async client, you first need to initialize it:

<CodeGroup>

```python Python
import os
from mem0 import AsyncMemoryClient

os.environ["MEM0_API_KEY"] = "your-api-key"

client = AsyncMemoryClient()
```

```javascript JavaScript
const { MemoryClient } = require('mem0ai');
const client = new MemoryClient({ apiKey: 'your-api-key'});
```

</CodeGroup>

## Methods

The `AsyncMemoryClient` provides the following methods:

### Add

Add a new memory asynchronously.

<CodeGroup>

```python Python
messages = [
    {"role": "user", "content": "<PERSON> loves playing badminton"},
    {"role": "assistant", "content": "That's great! <PERSON> is a fitness freak"},
]
await client.add(messages, user_id="alice")
```

```javascript JavaScript
const messages = [
    {"role": "user", "content": "Alice loves playing badminton"},
    {"role": "assistant", "content": "That's great! Alice is a fitness freak"},
];
await client.add(messages, { user_id: "alice" });
```

</CodeGroup>

### Search

Search for memories based on a query asynchronously.

<CodeGroup>

```python Python
await client.search("What is Alice's favorite sport?", user_id="alice")
```

```javascript JavaScript
await client.search("What is Alice's favorite sport?", { user_id: "alice" });
```

</CodeGroup>

### Get All

Retrieve all memories for a user asynchronously.

<CodeGroup>

```python Python
await client.get_all(user_id="alice")
```

```javascript JavaScript
await client.getAll({ user_id: "alice" });
```

</CodeGroup>

### Delete

Delete a specific memory asynchronously.

<CodeGroup>

```python Python
await client.delete(memory_id="memory-id-here")
```

```javascript JavaScript
await client.delete("memory-id-here");
```

</CodeGroup>

### Delete All

Delete all memories for a user asynchronously.

<CodeGroup>

```python Python
await client.delete_all(user_id="alice")
```

```javascript JavaScript
await client.deleteAll({ user_id: "alice" });
```

</CodeGroup>

### History

Get the history of a specific memory asynchronously.

<CodeGroup>

```python Python
await client.history(memory_id="memory-id-here")
```

```javascript JavaScript
await client.history("memory-id-here");
```

</CodeGroup>

### Users

Get all users, agents, and runs which have memories associated with them asynchronously.

<CodeGroup>

```python Python
await client.users()
```

```javascript JavaScript
await client.users();
```

</CodeGroup>

### Reset

Reset the client, deleting all users and memories asynchronously.

<CodeGroup>

```python Python
await client.reset()
```

```javascript JavaScript
await client.reset();
```

</CodeGroup>

## Conclusion

The `AsyncMemoryClient` provides a powerful way to interact with the Mem0 API asynchronously, allowing for more efficient and responsive applications. By using this client, you can perform memory operations without blocking your application's execution.

If you have any questions or need further assistance, please don't hesitate to reach out:

<Snippet file="get-help.mdx" />
