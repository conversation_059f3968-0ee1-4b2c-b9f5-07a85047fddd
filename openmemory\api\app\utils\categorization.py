import json
import logging
import os

import google.generativeai as genai
from typing import List
from dotenv import load_dotenv
from pydantic import BaseModel
from tenacity import retry, stop_after_attempt, wait_exponential
from app.utils.prompts import MEMORY_CATEGORIZATION_PROMPT

load_dotenv()

# Configure Gemini
api_key = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=api_key)
gemini_client = genai.GenerativeModel("gemini-1.5-flash")


class MemoryCategories(BaseModel):
    categories: List[str]


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=15))
def get_categories_for_memory(memory: str) -> List[str]:
    """Get categories for a memory."""
    try:
        prompt = f"{MEMORY_CATEGORIZATION_PROMPT}\n\nMemory: {memory}\n\nPlease respond with a JSON object containing a 'categories' array."

        response = gemini_client.generate_content(
            prompt,
            generation_config=genai.GenerationConfig(
                temperature=0,
                response_mime_type="application/json"
            )
        )

        response_json = json.loads(response.text)
        categories = response_json.get('categories', [])
        categories = [cat.strip().lower() for cat in categories]
        # TODO: Validate categories later may be
        return categories
    except Exception as e:
        logging.error(f"Error getting categories for memory: {e}")
        # Return empty list as fallback
        return []
