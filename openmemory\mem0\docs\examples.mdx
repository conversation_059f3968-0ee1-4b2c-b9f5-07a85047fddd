---
title: Overview
description: How to use mem0 in your existing applications?
---

<Snippet file="paper-release.mdx" />


With Mem0, you can create stateful LLM-based applications such as chatbots, virtual assistants, or AI agents. Mem0 enhances your applications by providing a memory layer that makes responses:

- More personalized
- More reliable
- Cost-effective by reducing the number of LLM interactions
- More engaging
- Enables long-term memory

Here are some examples of how Mem0 can be integrated into various applications:

## Examples

Explore how **Mem0** can power real-world applications and bring personalized, intelligent experiences to life:

<CardGroup cols={2}>  
  <Card title="AI Companion in Node.js" icon="node" href="/examples/ai_companion_js">  
    Build a personalized AI Companion in **Node.js** that remembers conversations and adapts over time using Mem0.  
  </Card>  

  <Card title="Mem0 with Ollama" icon="server" href="/examples/mem0-with-ollama">  
    Run **Mem0 locally** with **Ollama** to create private, stateful AI experiences without relying on cloud APIs.  
  </Card>  

  <Card title="Personal AI Tutor" icon="graduation-cap" href="/examples/personal-ai-tutor">  
    Create an **AI Tutor** that adapts to student progress, learning style, and history — for a truly customized learning experience.  
  </Card>  

  <Card title="Personal Travel Assistant" icon="plane" href="/examples/personal-travel-assistant">  
    Develop a **Personal Travel Assistant** that remembers your preferences, past trips, and helps plan future adventures.  
  </Card>  

  <Card title="Customer Support Agent" icon="headset" href="/examples/customer-support-agent">  
    Build a **Customer Support AI** that recalls user preferences, past chats, and provides context-aware, efficient help.  
  </Card>  

  <Card title="LlamaIndex + Mem0" icon="book-open" href="/examples/llama-index-mem0">  
    Combine **LlamaIndex** and Mem0 to create a powerful **ReAct Agent** with persistent memory for smarter interactions.  
  </Card>  

  <Card title="Chrome Extension" icon="puzzle-piece" href="/examples/chrome-extension">  
    Add **long-term memory** to ChatGPT, Claude, or Perplexity via the **Mem0 Chrome Extension** — personalize your AI chats anywhere.  
  </Card>  

  <Card title="YouTube Assistant" icon="puzzle-piece" href="/examples/youtube-assistant">  
    Integrate **Mem0** into **YouTube's** native UI, providing personalized responses with video context. 
  </Card> 

  <Card title="Document Writing Assistant" icon="pen" href="/examples/document-writing">  
    Create a **Writing Assistant** that understands and adapts to your unique style, improving consistency and productivity.  
  </Card>  

  <Card title="Multimodal AI Demo" icon="image" href="/examples/multimodal-demo">  
    Supercharge AI with **Mem0's multimodal memory** — blend text, images, and more for richer, context-aware interactions.  
  </Card>  

  <Card title="Personalized Research Agent" icon="magnifying-glass" href="/examples/personalized-deep-research">  
    Build a **Deep Research AI** that remembers your research goals and compiles insights from vast information sources.  
  </Card>  

  <Card title="Mem0 as an Agentic Tool" icon="robot" href="/examples/mem0-agentic-tool">  
    Integrate Mem0's memory capabilities with OpenAI's Agents SDK to create AI agents with persistent memory.  
  </Card>  

  <Card title="OpenAI Inbuilt Tools" icon="robot" href="/examples/openai-inbuilt-tools">  
    Use Mem0's memory capabilities with OpenAI's Inbuilt Tools to create AI agents with persistent memory.  
  </Card>  

  <Card title="Mem0 OpenAI Voice Demo" icon="microphone" href="/examples/mem0-openai-voice-demo">  
    Use Mem0's memory capabilities with OpenAI's Inbuilt Tools to create AI agents with persistent memory.  
  </Card>

  <Card title="Healthcare Assistant Google ADK" icon="microphone" href="/examples/mem0-google-adk-healthcare-assistant">
    Build a personalized healthcare assistant with persistent memory using Google's ADK and Mem0.
  </Card>

  <Card title="Email Processing" icon="envelope" href="/examples/email_processing">  
    Use Mem0's memory capabilities to process emails and create AI agents with persistent memory.  
  </Card> 
</CardGroup>
