---
title: 'OpenAI Assistant'
---

### Arguments

<ParamField path="name" type="string">
  Name for your AI assistant
</ParamField>

<ParamField path="instructions" type="string">
  how the Assistant and model should behave or respond
</ParamField>

<ParamField path="assistant_id" type="string">
  Load existing OpenAI Assistant. If you pass this, you don't have to pass other arguments.
</ParamField>

<ParamField path="thread_id" type="string">
  Existing OpenAI thread id if exists
</ParamField>

<ParamField path="model" type="str" default="gpt-4-1106-preview">
  OpenAI model to use
</ParamField>

<ParamField path="tools" type="list">
  OpenAI tools to use. Default set to `[{"type": "retrieval"}]`
</ParamField>

<ParamField path="data_sources" type="list" default="[]">
  Add data sources to your assistant. You can add in the following format: `[{"source": "https://example.com", "data_type": "web_page"}]`
</ParamField>

<ParamField path="telemetry" type="boolean" default="True">
  Anonymous telemetry (doesn't collect any user information or user's files). Used to improve the Embedchain package utilization. Default is `True`.
</ParamField>

## Usage

For detailed guidance on creating your own OpenAI Assistant, click the link below. It provides step-by-step instructions to help you through the process:

<Card title="Guide to Creating Your OpenAI Assistant" icon="link" href="/examples/openai-assistant">
  Learn how to build an OpenAI Assistant using the `OpenAIAssistant` class.
</Card>
