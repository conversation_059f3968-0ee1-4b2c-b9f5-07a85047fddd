---
title: "Product Updates"
mode: "wide"
---

<Snippet file="paper-release.mdx" />

<Tabs>
<Tab title="Python">

<Update label="2025-05-10" description="v0.1.100">

**New Features:**
- **Memory:** Added Group Chat Memory Feature support
- **Examples:** Added Healthcare assistant using Mem0 and Google ADK

**Bug Fixes:**
- **SSE:** Fixed SSE connection issues
- **MCP:** Fixed memories not appearing in MCP clients added from Dashboard

</Update>

<Update label="2025-05-07" description="v0.1.99">

**New Features:**
- **OpenMemory:** Added OpenMemory support
- **Neo4j:** Added weights to Neo4j model
- **AWS:** Added support for Opsearch Serverless
- **Examples:** Added ElizaOS Example

**Improvements:**
- **Documentation:** Updated Azure AI documentation
- **AI SDK:** Added missing parameters and updated demo application
- **OSS:** Fixed AOSS and AWS BedRock LLM

</Update>

<Update label="2025-04-30" description="v0.1.98">

**New Features:**
- **Neo4j:** Added support for Neo4j database
- **AWS:** Added support for AWS Bedrock Embeddings

**Improvements:**
- **Client:** Updated delete_users() to use V2 API endpoints
- **Documentation:** Updated timestamp and dual-identity memory management docs
- **Neo4j:** Improved Neo4j queries and removed warnings
- **AI SDK:** Added support for graceful failure when services are down

**Bug Fixes:**
- Fixed AI SDK filters
- Fixed new memories wrong type
- Fixed duplicated metadata issue while adding/updating memories

</Update>

<Update label="2025-04-23" description="v0.1.97">

**New Features:**
- **HuggingFace:** Added support for HF Inference

**Bug Fixes:**
- Fixed proxy for Mem0

</Update>

<Update label="2025-04-16" description="v0.1.96">

**New Features:**
- **Vercel AI SDK:** Added Graph Memory support

**Improvements:**
- **Documentation:** Fixed timestamp and README links
- **Client:** Updated TS client to use proper types for deleteUsers
- **Dependencies:** Removed unnecessary dependencies from base package

</Update>

<Update label="2025-04-09" description="v0.1.95">

**Improvements:**
- **Client:** Fixed Ping Method for using default org_id and project_id
- **Documentation:** Updated documentation

**Bug Fixes:**
- Fixed mem0-migrations issue

</Update>

<Update label="2025-04-26" description="v0.1.94">

**New Features:**
- **Integrations:** Added Memgraph integration
- **Memory:** Added timestamp support
- **Vector Stores:** Added reset function for VectorDBs

**Improvements:**
- **Documentation:** 
  - Updated timestamp and expiration_date documentation
  - Fixed v2 search documentation
  - Added "memory" in EC "Custom config" section
  - Fixed typos in the json config sample

</Update>

<Update label="2025-04-21" description="v0.1.93">

**Improvements:**
- **Vector Stores:** Initialized embedding_model_dims in all vectordbs

**Bug Fixes:**
- **Documentation:** Fixed agno link

</Update>

<Update label="2025-04-18" description="v0.1.92">

**New Features:**
- **Memory:** Added Memory Reset functionality
- **Client:** Added support for Custom Instructions
- **Examples:** Added Fitness Checker powered by memory

**Improvements:**
- **Core:** Updated capture_event
- **Documentation:** Fixed curl for v2 get_all

**Bug Fixes:**
- **Vector Store:** Fixed user_id functionality
- **Client:** Various client improvements

</Update>

<Update label="2025-04-16" description="v0.1.91">

**New Features:**
- **LLM Integrations:** Added Azure OpenAI Embedding Model
- **Examples:** 
  - Added movie recommendation using grok3
  - Added Voice Assistant using Elevenlabs

**Improvements:**
- **Documentation:** 
  - Added keywords AI
  - Reformatted navbar page URLs
  - Updated changelog
  - Updated openai.mdx
- **FAISS:** Silenced FAISS info logs

</Update>

<Update label="2025-04-11" description="v0.1.90">

**New Features:**
- **LLM Integrations:** Added Mistral AI as LLM provider

**Improvements:**
- **Documentation:** 
  - Updated changelog
  - Fixed memory exclusion example
  - Updated xAI documentation
  - Updated YouTube Chrome extension example documentation

**Bug Fixes:**
- **Core:** Fixed EmbedderFactory.create() in GraphMemory
- **Azure OpenAI:** Added patch to fix Azure OpenAI
- **Telemetry:** Fixed telemetry issue

</Update>

<Update label="2025-04-11" description="v0.1.89">

**New Features:**
- **Langchain Integration:** Added support for Langchain VectorStores
- **Examples:** 
  - Added personal assistant example
  - Added personal study buddy example
  - Added YouTube assistant Chrome extension example
  - Added agno example
  - Updated OpenAI Responses API examples
- **Vector Store:** Added capability to store user_id in vector database
- **Async Memory:** Added async support for OSS

**Improvements:**
- **Documentation:** Updated formatting and examples

</Update>

<Update label="2025-04-09" description="v0.1.87">

**New Features:**
- **Upstash Vector:** Added support for Upstash Vector store

**Improvements:**
- **Code Quality:** Removed redundant code lines
- **Build:** Updated MAKEFILE
- **Documentation:** Updated memory export documentation

</Update>

<Update label="2025-04-07" description="v0.1.86">

**Improvements:**
- **FAISS:** Added embedding_dims parameter to FAISS vector store

</Update>

<Update label="2025-04-07" description="v0.1.84">

**New Features:**
- **Langchain Embedder:** Added Langchain embedder integration

**Improvements:**
- **Langchain LLM:** Updated Langchain LLM integration to directly pass the Langchain object LLM
</Update>

<Update label="2025-04-07" description="v0.1.83">

**Bug Fixes:**
- **Langchain LLM:** Fixed issues with Langchain LLM integration
</Update>

<Update label="2025-04-07" description="v0.1.82">

**New Features:**
- **LLM Integrations:** Added support for Langchain LLMs, Google as new LLM and embedder
- **Development:** Added development docker compose

**Improvements:**
- **Output Format:** Set output_format='v1.1' and updated documentation

**Documentation:**
- **Integrations:** Added LMStudio and Together.ai documentation
- **API Reference:** Updated output_format documentation
- **Integrations:** Added PipeCat integration documentation
- **Integrations:** Added Flowise integration documentation for Mem0 memory setup

**Bug Fixes:**
- **Tests:** Fixed failing unit tests
</Update>

<Update label="2025-04-02" description="v0.1.79">

**New Features:**
- **FAISS Support:** Added FAISS vector store support

</Update>

<Update label="2025-04-02" description="v0.1.78">

**New Features:**
- **Livekit Integration:** Added Mem0 livekit example
- **Evaluation:** Added evaluation framework and tools

**Documentation:**
- **Multimodal:** Updated multimodal documentation
- **Examples:** Added examples for email processing
- **API Reference:** Updated API reference section
- **Elevenlabs:** Added Elevenlabs integration example

**Bug Fixes:**
- **OpenAI Environment Variables:** Fixed issues with OpenAI environment variables
- **Deployment Errors:** Added `package.json` file to fix deployment errors
- **Tools:** Fixed tools issues and improved formatting
- **Docs:** Updated API reference section for `expiration date`
</Update>

<Update label="2025-03-26" description="v0.1.77">

**Bug Fixes:**
- **OpenAI Environment Variables:** Fixed issues with OpenAI environment variables
- **Deployment Errors:** Added `package.json` file to fix deployment errors
- **Tools:** Fixed tools issues and improved formatting
- **Docs:** Updated API reference section for `expiration date`
</Update>

<Update label="2025-03-19" description="v0.1.76">
**New Features:**
- **Supabase Vector Store:** Added support for Supabase Vector Store
- **Supabase History DB:** Added Supabase History DB to run Mem0 OSS on Serverless
- **Feedback Method:** Added feedback method to client

**Bug Fixes:**
- **Azure OpenAI:** Fixed issues with Azure OpenAI
- **Azure AI Search:** Fixed test cases for Azure AI Search
</Update>

</Tab>

<Tab title="TypeScript">

<Update label="2025-05-30" description="v2.1.29">
**Improvements:**
- **Client:** Added Async Mode Param for `add` method.
</Update>

<Update label="2025-05-30" description="v2.1.28">
**Improvements:**
- **SDK:** Update Google SDK Peer Dependency Version.
</Update>

<Update label="2025-05-27" description="v2.1.27">
**Improvements:**
- **OSS:** Added baseURL param in LLM Config.
</Update>
  
<Update label="2025-05-23" description="v2.1.26">
**Improvements:**
- **Client:** Removed type `string` from `messages` interface
</Update>

<Update label="2025-05-08" description="v2.1.25">
**Improvements:**
- **Client:** Improved error handling in client.
</Update>

<Update label="2025-05-06" description="v2.1.24">
**New Features:**
- **Client:** Added new param `output_format` to match Python SDK.
- **Client:** Added new enum `OutputFormat` for `v1.0` and `v1.1`
</Update>

<Update label="2025-05-05" description="v2.1.23">
**New Features:**
- **Client:** Updated `deleteUsers` to use `v2` API.
- **Client:** Deprecated `deleteUser` and added deprecation warning.
</Update>

<Update label="2025-05-02" description="v2.1.22">
**New Features:**
- **Client:** Updated `deleteUser` to use `entity_id` and `entity_type`
</Update>

<Update label="2025-05-01" description="v2.1.21">
**Improvements:**
- **OSS SDK:** Bumped version of `@anthropic-ai/sdk` to `0.40.1`
</Update>

<Update label="2025-04-28" description="v2.1.20">
**Improvements:**
- **Client:** Fixed `organizationId` and `projectId` being asssigned to default in `ping` method
</Update>

<Update label="2025-04-22" description="v2.1.19">
**Improvements:**
- **Client:** Added support for `timestamps`
</Update>

<Update label="2025-04-17" description="v2.1.18">
**Improvements:**
- **Client:** Added support for custom instructions
</Update>

<Update label="2025-04-15" description="v2.1.17">
**New Features:**
- **OSS SDK:** Added support for Langchain LLM
- **OSS SDK:** Added support for Langchain Embedder
- **OSS SDK:** Added support for Langchain Vector Store
- **OSS SDK:** Added support for Azure OpenAI Embedder


**Improvements:**
- **OSS SDK:** Changed `model` in LLM and Embedder to use type any from `string` to use langchain llm models
- **OSS SDK:** Added client to vector store config for langchain vector store
- **OSS SDK:** - Updated Azure OpenAI to use new OpenAI SDK
</Update>

<Update label="2025-04-11" description="v2.1.16-patch.1">
**Bug Fixes:**
- **Azure OpenAI:** Fixed issues with Azure OpenAI
</Update>

<Update label="2025-04-11" description="v2.1.16">
**New Features:**
- **Azure OpenAI:** Added support for Azure OpenAI
- **Mistral LLM:** Added Mistral LLM integration in OSS

**Improvements:**
- **Zod:** Updated Zod to 3.24.1 to avoid conflicts with other packages
</Update>

<Update label="2025-04-09" description="v2.1.15">
**Improvements:**
- **Client:** Added support for Mem0 to work with Chrome Extensions
</Update>

<Update label="2025-04-01" description="v2.1.14">
**New Features:**
- **Mastra Example:** Added Mastra example
- **Integrations:** Added Flowise integration documentation for Mem0 memory setup

**Improvements:**
- **Demo:** Updated Demo Mem0AI
- **Client:** Enhanced Ping method in Mem0 Client
- **AI SDK:** Updated AI SDK implementation
</Update>

<Update label="2025-03-29" description="v2.1.13">
**Improvements:**
- **Introuced `ping` method to check if API key is valid and populate org/project id**
</Update>

<Update label="2025-03-29" description="AI SDK v1.0.0">
**New Features:**
- **Vercel AI SDK Update:** Support threshold and rerank

**Improvements:**
- **Made add calls async to avoid blocking**
- **Bump `mem0ai` to use `2.1.12`**

</Update>

<Update label="2025-03-26" description="v2.1.12">
**New Features:**
- **Mem0 OSS:** Support infer param

**Improvements:**
- **Updated Supabase TS Docs**
- **Made package size smaller**

</Update>

<Update label="2025-03-19" description="v2.1.11">
**New Features:**
- **Supabase Vector Store Integration**
- **Feedback Method**
</Update>

</Tab>

<Tab title="Platform">

<Update label="2025-05-19" description="">

**Bug Fixes:**
- **Core:** Fixed unicode error in user_id, agent_id, run_id and app_id

</Update>

<Update label="2025-05-17" description="">

**New Features:**
- **Graph:** Added Neo4J Graph Migration
- **API:** Added API to set custom instructions

</Update>

<Update label="2025-05-16" description="">

**New Features:**
- **API:** Added Org-wide API Limit and Usage

**Improvements:**
- **Database:** Added migration for "is_deleted" column
- **Graph:** Improved graph queries

</Update>

<Update label="2025-05-15" description="">

**New Features:**
- **Lambda:** Added actions to lambda
- **Core:** Added background runs support
- **Models:** Added o4-mini for pro users

</Update>

<Update label="2025-05-10" description="">

**New Features:**
- **Integrations:** Added Intercom Events integration
- **Billing:** Added prefilled email for payments
- **Organizations:** Added Pro organization marking

**Improvements:**
- **UI:** Fixed loading jitter for organization selection
- **Infrastructure:** Improved production scaling

</Update>

<Update label="2025-05-09" description="">

**Improvements:**
- **Memory:** Fixed filters in Memory Page
- **Deployment:** Added custom categories for on-premise

</Update>

<Update label="2025-05-08" description="">

**Improvements:**
- **Backend:** Updated Django settings for metrics
- **Memory:** Added retries to memory filtering
- **Search:** Added scoring mechanism

</Update>

<Update label="2025-05-07" description="">

**Improvements:**
- **Deployment:** Updated deployment scripts
- **Testing:** Added code coverage tracking
- **Memory:** Added background cron job for memory quality

</Update>

<Update label="2025-05-06" description="">

**New Features:**
- **Models:** Added support for 4.1-mini model

**Improvements:**
- **Infrastructure:** Increased instance count
- **API:** Added V2 for Manage Entities

</Update>

<Update label="2025-05-04" description="">

**New Features:**
- **Testing:** Added code coverage tracking
- **AI:** Added Keywords AI integration

**Improvements:**
- **UI:** Updated UI with tabs
- **Database:** Added migrations for custom instructions
- **Search:** Added criteria filtering

</Update>

<Update label="2025-04-26" description="">

**Improvements:**
- **Performance:** Parallelized embedding calls
- **Monitoring:** Added timing for LLM calls
- **Search:** Added category checking in Search V2
- **Bug Fixes:** Fixed issues with ADD filters
- **Graph:** Implemented new graph updates

</Update>

<Update label="2025-04-25" description="">

**Improvements:**
- **Memory:** Fixed memory export functionality
- **Analytics:** Added logging for project

</Update>

<Update label="2025-04-24" description="">

**Improvements:**
- **Output:** Added memory_type display for ADD output

</Update>

<Update label="2025-04-23" description="">

**New Features:**
- **UI:** Added new Pricing Component
- **Memory:** Implemented Long/Short term memory categorization
- **Output:** Modified serializer to hide memory_type

**Documentation:**
- Updated README for deployment

</Update>

<Update label="2025-04-22" description="">

**New Features:**
- **Memory:** Added timestamp to ADD call

**Bug Fixes:**
- Fixed issues with coreV2

</Update>

<Update label="2025-04-21" description="">

**New Features:**
- **Memory:** Implemented backdating with migrations and backfilling script

</Update>

<Update label="2025-04-17" description="">

**New Features:**
- **Billing:** Integrated Stripe Billing Dashboard
- **Admin:** Added webhook creation functionality

**Bug Fixes:**
- Fixed Users Page issues
- Fixed Custom Categories
- Fixed Table components
- Updated Stripe configuration

</Update>

<Update label="2025-04-16" description="">

**Improvements:**
- **Performance:** Made Admin panel and Memory Page faster
- **Security:** Implemented active session cancellation
- **Analytics:** Added Stripe customer ID capture

</Update>

<Update label="2025-04-12" description="">

**New Features:**
- **Memory Management:** 
  - Added ability to delete memories from Project level with filters
  - Added delete memories capability on Memories Page
- **Memory Visualization:** Released V1 Graph Memory Visualization
- **Graph Playground:** Enabled for @mem0.ai users
- **Notifications:** Added email alerts to organization owners when new members join
- **Memory Export:** Added date support for filtering memory exports

**Improvements:**
- **Performance:** 
  - Optimized graph for better performance
  - Optimized database calls in ADD method
- **Analytics:** Added flagging of paid users in Posthog
- **CI/CD:** Improved CI pipeline and fixed lint issues

</Update>

<Update label="2025-04-10" description="">

**New Features:**
- **Notifications:** Implemented email notifications for organization owners when new members join

**Improvements:**
- **CI/CD:** Fixed Dockerfile for CI tests

</Update>

<Update label="2025-04-09" description="">

**Improvements:**
- **Integrations:** Updated chat model for Together Qwen
- **Platform:** Removed older platforms
- **Bug Fixes:** Fixed FILTER_MAPPING

</Update>

<Update label="2025-04-03" description="">

**New Features:**
- **Memory:** Added implicit memory capabilities
- **API:** Improved implicit lambda and get_all v2 functionality

</Update>

<Update label="2025-04-02" description="">

**New Features:**
- **Integrations:** Added Clay integration

**Improvements:**
- **Integrations:** Removed deepseek coder from Together
- **API:** Added custom instructions for add v2

</Update>

<Update label="2025-03-31" description="">

**Security:**
- **Validation:** Added key validation in messages

</Update>

<Update label="2025-03-28" description="">
- **Updated Playground Prompt**
- **Send Email on User Addition to Org/Proj**
- **Fix Search Entity**
</Update>

<Update label="2025-03-19" description="">
- **General Stability & Performance Improvements**
</Update>

</Tab>

<Tab title="Vercel AI SDK">

<Update label="2025-05-23" description="v1.0.5">
**New Features:**
- **Vercel AI SDK:** Added support for Google provider.
</Update>

<Update label="2025-05-10" description="v1.0.4">
**New Features:**
- **Vercel AI SDK:** Added support for new param `output_format`.
</Update>

<Update label="2025-05-08" description="v1.0.3">
**Improvements:**
- **Vercel AI SDK:** Added support for graceful failure in cases services are down. 
</Update>

<Update label="2025-05-01" description="v1.0.1">
**New Features:**
- **Vercel AI SDK:** Added support for graph memories
</Update>

</Tab>

</Tabs>

